'use client'

import { use<PERSON><PERSON>back, useEffect, useMemo, useRef, useState } from 'react'
import Image from 'next/image'
import { Splide, SplideSlide } from '@splidejs/react-splide'
import { ButtonUrl, RelationItem } from '@ninebot/core'
import { useNavigate } from '@ninebot/core/src/businessHooks'
import { Button } from 'antd'
import clsx from 'clsx'
import Hls from 'hls.js'
import ReactPlayer from 'react-player'

import { IconArrow } from '@/components'
import { Arrow } from '@/components'

import styles from './index.module.css'

import '@splidejs/react-splide/css'

type BannerProps = {
  bannerData: RelationItem[] | null
}

interface CarouselProps {
  go: (index: number | string) => void
  splide?: {
    options: Record<string, unknown>
    refresh: () => void
  }
}

// HLS错误处理函数
const handleHlsError = (error: { fatal?: boolean; type?: string; details?: string }) => {
  console.error('Banner HLS Error:', error)

  if (error.fatal) {
    switch (error.type) {
      case Hls.ErrorTypes.NETWORK_ERROR:
        console.error('Banner网络错误，尝试恢复...', error.details)
        break
      case Hls.ErrorTypes.MEDIA_ERROR:
        console.error('Banner媒体错误，尝试恢复...', error.details)
        break
      default:
        console.error('Banner无法恢复的错误:', error.details)
        break
    }
  }
}

const Banner = ({ bannerData }: BannerProps) => {
  const [activeIndex, setActiveIndex] = useState(0)
  const carouselRef = useRef<CarouselProps>(null)
  const videoListRef = useRef<HTMLVideoElement[]>([])
  const [bannerIndex, setBannerIndex] = useState(0)
  // 第一个数据为视频的时候，等它加载完
  const [firstVideoLoad, setFirstVideoLoad] = useState(false)
  const bannerTimer = useRef<NodeJS.Timeout>()
  const uuid = useRef(0)
  const [isClient, setIsClient] = useState(false) // 添加客户端检测
  const { openPage } = useNavigate()

  const bannerTotal = useMemo(() => {
    return bannerData?.length || 0
  }, [bannerData])

  const playDotsAnimation = useCallback(async (index: number) => {
    const elems = document.querySelectorAll('.progressLine')
    const len = elems.length
    const r = document.querySelectorAll('.progressLine')[index] as HTMLElement
    let n: number | undefined
    let O = 5000
    window.cancelAnimationFrame(uuid.current)

    // 处理视频的播放 - 添加异步处理和错误捕获
    if (videoListRef.current[index]) {
      try {
        // 确保视频已准备好播放
        const video = videoListRef.current[index]
        if (video.readyState >= 2) {
          // HAVE_CURRENT_DATA
          await video.play()
          O = video.duration * 1000 // 转换为毫秒
        } else {
          // 如果视频还没准备好，等待loadeddata事件
          await new Promise<void>((resolve) => {
            const handleLoadedData = () => {
              video.removeEventListener('loadeddata', handleLoadedData)
              resolve()
            }
            video.addEventListener('loadeddata', handleLoadedData)
          })
          await video.play()
          O = video.duration * 1000
        }
      } catch (error) {
        // 捕获AbortError和其他播放错误
        if (error instanceof Error && error.name !== 'AbortError') {
          console.warn('Banner视频播放失败:', error.message)
        }
        // 如果视频播放失败，使用默认时长
        O = 5000
      }
    }

    if (!r && len > 1) {
      return
    }
    const s = (Number.isNaN(parseInt(r.style.width)) ? 0 : parseInt(r.style.width)) / 100
    uuid.current = window.requestAnimationFrame(function e(t) {
      if (n === undefined && n !== 0) {
        n = t - O * s
      }
      let a = t - n
      if (videoListRef.current[index]) {
        a = videoListRef.current[index].currentTime * 1000 // 转换为毫秒
      }
      if (a < O) {
        const o = Math.min((a / O) * 100, 100)
        r.style.width = `${o}%`
        elems.forEach((item, j) => {
          if (index > j) {
            const i = item as HTMLElement
            i.style.width = '100%'
          }
        })
        uuid.current = window.requestAnimationFrame(e)
      } else {
        if (index === len - 1) {
          elems.forEach((item) => {
            const i = item as HTMLElement
            i.style.width = '0%'
          })
        }
        carouselRef.current?.go('>')
      }
    })
  }, [])

  const handleNext = () => {
    stopDotsAnimation()
    let index = bannerIndex + 1
    if (index > bannerTotal - 1) {
      index = 0
    }
    handleClickDot(index)
  }

  const handlePrev = () => {
    let index = bannerIndex - 1
    if (index < 0) {
      index = bannerTotal - 1
    }
    handleClickDot(index)
  }

  const handleClickDot = (index: number) => {
    if (index === bannerIndex) return
    stopDotsAnimation()
    const elems = document.querySelectorAll('.progressLine')
    elems.forEach((item, j) => {
      const i = item as HTMLElement
      if (index > j) {
        i.style.width = '100%'
      } else {
        i.style.width = '0%'
      }
    })

    carouselRef.current?.go(index)
  }
  const stopDotsAnimation = () => {
    window.cancelAnimationFrame(uuid.current)
    if (videoListRef.current[activeIndex]) {
      try {
        const video = videoListRef.current[activeIndex]
        if (!video.paused) {
          video.pause()
        }
      } catch (error) {
        // 忽略暂停时的错误
        console.warn('Banner视频暂停失败:', error)
      }
    }
  }

  const handleSideChange = async (index: number) => {
    setActiveIndex(index)

    // 先停止所有视频播放并重置进度
    const stopAllVideos = async () => {
      const promises = videoListRef.current.map(async (item) => {
        if (item) {
          try {
            if (!item.paused) {
              item.pause()
            }
            // 等待一小段时间确保暂停完成
            await new Promise((resolve) => setTimeout(resolve, 50))
            item.currentTime = 0
          } catch (error) {
            console.warn('Banner停止视频失败:', error)
          }
        }
      })
      await Promise.all(promises)
    }

    // 先停止所有视频，然后播放当前视频
    await stopAllVideos()
    // 添加小延迟确保视频状态稳定
    setTimeout(() => {
      playDotsAnimation(index).catch((error) => {
        console.warn('Banner切换播放失败:', error)
      })
    }, 100)
  }

  // 客户端检测
  useEffect(() => {
    setIsClient(true)
  }, [])

  useEffect(() => {
    if (!bannerData?.[0]?.video_url) {
      setFirstVideoLoad(true)
      // 异步调用playDotsAnimation
      playDotsAnimation(0).catch((error) => {
        console.warn('Banner初始化播放失败:', error)
      })
    }
  }, [bannerData, playDotsAnimation])

  useEffect(() => {
    if (bannerTimer.current) {
      return
    }

    if (firstVideoLoad) {
      bannerTimer.current = setInterval(() => {
        if (carouselRef.current) {
          clearInterval(bannerTimer.current)
          // 异步调用playDotsAnimation
          playDotsAnimation(0).catch((error) => {
            console.warn('Banner定时器播放失败:', error)
          })
        }
      }, 1000)
    }
  }, [playDotsAnimation, firstVideoLoad])

  const handleBeforeChange = (_from: number, to: number) => {
    // 确保索引在有效范围内
    if (to >= 0 && (!bannerData || to < bannerData?.length)) {
      handleClickDot(to)
    }
  }
  const setRef = (index: number) => (el: ReactPlayer) => {
    videoListRef.current[index] = el?.getInternalPlayer() as HTMLVideoElement
  }

  const renderVideo = (item: RelationItem, index: number) => {
    return (
      <div className="absolute inset-0 h-full w-full overflow-hidden">
        {isClient ? (
          <ReactPlayer
            url={item?.video_url || ''}
            controls={false}
            ref={setRef(index)}
            playsinline
            muted={true}
            width="100%"
            height="100%"
            style={{
              objectFit: 'cover',
              position: 'absolute',
              top: 0,
              left: 0,
              width: '100%',
              height: '100%',
            }}
            onEnded={() => {
              const video = videoListRef.current[index]
              if (video) {
                video.currentTime = 0
                // 视频播放完成，设置当前进度条为100%
                const progressLine = document.querySelectorAll('.progressLine')[
                  index
                ] as HTMLElement
                if (progressLine) {
                  progressLine.style.width = '100%'
                }

                // 添加防抖，避免立即触发多次切换
                if (index === activeIndex) {
                  // 使用setTimeout避免与其他切换逻辑冲突
                  setTimeout(() => {
                    if (index === activeIndex) {
                      // 检查是否是最后一个索引
                      const nextIndex = (index + 1) % (bannerData?.length || 1)
                      if (nextIndex === 0 && index === (bannerData?.length || 1) - 1) {
                        // 如果是从最后一个切换到第一个，特殊处理
                        setActiveIndex(0) // 先更新状态
                      }
                      handleBeforeChange(index, nextIndex)
                    }
                  }, 100)
                }
              }
            }}
            onError={(error) => {
              console.error('Banner ReactPlayer error:', error)
              // 特殊处理HLS相关错误
              if (error && typeof error === 'object' && 'type' in error) {
                handleHlsError(error as { fatal?: boolean; type?: string; details?: string })
              }
            }}
            config={{
              file: {
                attributes: {
                  preload: 'metadata',
                },
                hlsOptions: {
                  maxBufferLength: 30,
                  maxBufferSize: 60 * 1000 * 1000, // 60MB
                  enableWorker: true,
                  // 添加错误处理
                  errorController: {
                    onError: handleHlsError,
                  },
                  // 确保使用本地hls.js而不是CDN
                  loader: undefined, // 让ReactPlayer使用已经导入的Hls对象
                },
              },
            }}
          />
        ) : (
          // 服务端渲染时显示占位符
          <div
            className="absolute inset-0 h-full w-full bg-black"
            style={{
              objectFit: 'cover',
              position: 'absolute',
              top: 0,
              left: 0,
              width: '100%',
              height: '100%',
            }}
          />
        )}
      </div>
    )
  }

  const getBannerHeight = () => {
    // 根据屏幕宽度计算Banner高度
    const width = window.innerWidth
    if (width >= 1440) {
      return 800
    } else if (width >= 1024) {
      // 响应式计算高度 - 与CSS中的clamp函数保持一致
      const height = 500 + ((width - 1024) / 416) * 240
      return Math.round(height)
    } else {
      return 500
    }
  }

  const [bannerWidth, setBannerWidth] = useState(1024)

  // 添加窗口大小变化监听
  useEffect(() => {
    const handleResize = () => {
      const realWidth = Math.max(document.documentElement.clientWidth, 1024)
      setBannerWidth(realWidth)
      if (carouselRef.current?.splide) {
        carouselRef.current.splide.options = {
          ...carouselRef.current.splide.options,
          height: getBannerHeight(),
          width: realWidth,
        }
        carouselRef.current.splide.refresh()
      }
    }

    window.addEventListener('resize', handleResize)
    // 初始化时也执行一次
    handleResize()

    return () => {
      window.removeEventListener('resize', handleResize)
    }
  }, [])

  // 组件卸载时清理资源
  useEffect(() => {
    const currentVideoList = videoListRef.current // 保存当前ref值以避免cleanup函数中的警告
    const currentTimer = bannerTimer.current
    const currentUuid = uuid.current

    return () => {
      // 停止动画
      window.cancelAnimationFrame(currentUuid)

      // 清理定时器
      if (currentTimer) {
        clearInterval(currentTimer)
      }

      // 清理视频播放器，避免路由切换时的DOM异常
      currentVideoList.forEach((video, index) => {
        if (video) {
          try {
            video.pause()
            video.currentTime = 0
            // 如果是HLS视频，尝试销毁HLS实例
            if (video.src && video.src.includes('.m3u8')) {
              console.log(`清理Banner视频播放器 ${index}`)
            }
          } catch (error) {
            console.warn(`Banner视频播放器 ${index} 销毁时出现错误:`, error)
          }
        }
      })
    }
  }, [])

  return (
    <div
      className="group relative mx-auto w-full max-w-[2560px] overflow-hidden bg-black"
      style={{
        width: bannerWidth,
        height: getBannerHeight(),
      }}>
      <Splide
        ref={carouselRef}
        options={{
          type: 'loop',
          pagination: !1,
          drag: !1,
          arrows: !1,
          rewind: !0,
          perMove: 1,
          preloadPages: 2,
          interval: 5000,
          height: getBannerHeight(), // 使用动态计算的高度
          width: '100%',
        }}
        onMove={(_e: object, a: number) => {
          stopDotsAnimation()
          setBannerIndex(a)
          handleSideChange(a)
        }}>
        {bannerData?.map((item, index) => (
          <SplideSlide
            key={index}
            onMouseEnter={() => {
              stopDotsAnimation()
            }}
            onMouseLeave={() => {
              // 使用setTimeout避免立即播放造成的竞态条件
              setTimeout(() => {
                playDotsAnimation(index).catch((error) => {
                  console.warn('Banner鼠标离开播放失败:', error)
                })
              }, 50)
            }}>
            <div
              className="relative h-full w-full"
              onClick={() => openPage({ ...(item?.button_url as ButtonUrl) })}>
              <div className="absolute inset-0 flex h-full w-full flex-col items-center overflow-hidden">
                {item?.video_url ? (
                  renderVideo(item, index)
                ) : (
                  <div className="absolute inset-0 h-full w-full overflow-hidden">
                    <Image
                      src={item?.image_url_desktop || ''}
                      alt={item?.title || ''}
                      fill
                      className="object-cover"
                      priority
                      sizes="100vw"
                      style={{ objectPosition: 'center center' }}
                    />
                  </div>
                )}

                {/* 内容区域 */}
                <div className="relative mx-auto">
                  <div className="flex flex-col items-center pt-[66.5px] text-white">
                    {item?.subtitle && (
                      <div className="mb-[8px] truncate text-center font-miSansMedium380 text-[18px] leading-[120%] 2xl:text-[20px]">
                        {item?.subtitle}
                      </div>
                    )}
                    {item?.title && (
                      <div className="mb-[8px] truncate text-center font-miSansSemibold520 text-[40px] leading-[120%] 2xl:text-[64px]">
                        {item?.title}
                      </div>
                    )}
                    {item?.description && (
                      <div className="mb-[24px] truncate text-center font-miSansMedium380 text-[16px] leading-[140%] 2xl:text-[24px]">
                        {item?.description}
                      </div>
                    )}
                    {item?.button_text && (
                      <Button
                        className={clsx(
                          'min-w-[146px] font-miSansDemiBold450 text-[16px] leading-[20px]',
                          item?.button_arrow ? 'justify-between' : 'justify-center',
                        )}
                        type="primary">
                        {item?.button_text}
                        {item.button_arrow && <IconArrow color="#fff" size={20} rotate={-90} />}
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </SplideSlide>
        ))}
      </Splide>

      {/* 选项卡 */}
      {bannerTotal > 1 && (
        <>
          <div className="absolute bottom-0 left-0 h-[100px] w-full bg-[linear-gradient(360deg,rgba(0,0,0,0.5)_-71.82%,rgba(0,0,0,0)_88.64%)]">
            <div className="mx-auto flex w-fit justify-center">
              <div className="flex h-fit items-center justify-center text-[16px] text-white">
                {bannerData?.map((item, index) => (
                  <div
                    key={index}
                    className={`relative flex min-w-[140px] items-center justify-center px-[20px] pt-[15px] text-white ${activeIndex === index ? 'font-miSansSemiBold520 text-opacity-100' : 'text-opacity-60'} cursor-pointer border-t border-gray-3 text-[18px]`}
                    onClick={() => {
                      handleClickDot(index)
                    }}>
                    {/* activeprogressbar: 图片进度条动画 */}
                    <div
                      className={`progressLine absolute left-0 top-[-1px] h-[1px] w-0 bg-white ${activeIndex === index ? styles.activeprogressbar : ''}`}></div>

                    <div className="h-[50px] leading-[50px]">{item?.title || ''}</div>
                  </div>
                ))}
              </div>
            </div>
          </div>
          <div className="absolute bottom-0 left-0 top-0 ml-[48px] hidden text-white group-hover:block">
            <div className="flex h-full items-center justify-center">
              <button
                className="flex h-[48px] w-[48px] cursor-pointer items-center justify-center rounded-full bg-black/40 transition-colors duration-200 hover:bg-black/30 focus:bg-black/65 focus:outline-none active:bg-black/65"
                onClick={handlePrev}
                onMouseLeave={(e) => e.currentTarget.blur()}>
                <Arrow rotate={180} color="currentColor" />
              </button>
            </div>
          </div>
          <div className="absolute bottom-0 right-0 top-0 mr-[48px] hidden text-white group-hover:block">
            <div className="flex h-full items-center justify-center">
              <button
                className="flex h-[48px] w-[48px] cursor-pointer items-center justify-center rounded-full bg-black/40 transition-colors duration-200 hover:bg-black/30 focus:bg-black/65 focus:outline-none active:bg-black/65"
                onClick={handleNext}
                onMouseLeave={(e) => e.currentTarget.blur()}>
                <Arrow color="currentColor" />
              </button>
            </div>
          </div>
        </>
      )}
    </div>
  )
}

export default Banner
